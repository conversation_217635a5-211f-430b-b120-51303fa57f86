<?php

use Illuminate\Support\Facades\Auth;

if (!function_exists('tenant')) {
    function tenant(): ?\App\Models\Tenant
    {
        return app('tenant');
    }
}

if (!function_exists('tenant_id')) {
    function tenant_id(): ?int
    {
        return tenant()?->id;
    }
}

if (!function_exists('tenant_domain')) {
    function tenant_domain(): ?string
    {
        return config('tenant.domain');
    }
}

if (!function_exists('current_user_role')) {
    function current_user_role(): ?\App\Enums\UserRoleEnum
    {
        if (!Auth::check()) {
            return null;
        }

        return Auth::user()->getTenantRole();
    }
}

if (!function_exists('user_has_role')) {
    function user_has_role(\App\Enums\UserRoleEnum $role): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasRole($role);
    }
}

if (!function_exists('user_has_any_role')) {
    function user_has_any_role(array $roles): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasAnyRole($roles);
    }
}

if (!function_exists('user_has_min_role')) {
    function user_has_min_role(\App\Enums\UserRoleEnum $minRole): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasMinRole($minRole);
    }
}

if (!function_exists('user_is_manager')) {
    function user_is_manager(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->isManager();
    }
}

if (!function_exists('user_is_admin')) {
    function user_is_admin(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->isAdmin();
    }
}

if (!function_exists('user_is_user')) {
    function user_is_user(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->isUser();
    }
}

if (!function_exists('user_can_access_admin')) {
    function user_can_access_admin(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->canAccessAdmin();
    }
}

if (!function_exists('user_can_access_manager')) {
    function user_can_access_manager(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->canAccessManager();
    }
}

if (!function_exists('user_belongs_to_tenant')) {
    function user_belongs_to_tenant(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->belongsToTenant();
    }
}

if (!function_exists('tenant_setting')) {
    /**
     * Get tenant setting value
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    function tenant_setting($key = null, $default = null)
    {
        $tenant = request()->tenant ?? (session('tenant_id') ? 
            \App\Models\Tenant::find(session('tenant_id')) : null);

        if (!$tenant) {
            return $default;
        }

        if (is_null($key)) {
            return \App\Models\TenantSetting::where('tenant_id', $tenant->id)
                ->pluck('value', 'key')
                ->toArray();
        }

        $setting = \App\Models\TenantSetting::where('tenant_id', $tenant->id)
            ->where('key', $key)
            ->first();

        return $setting ? $setting->value : $default;
    }
}
