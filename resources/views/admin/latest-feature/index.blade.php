@extends('admin.layouts.app')

@section('breadcrumb', '<PERSON>ậ<PERSON> nhật tính năng mới')
@section('content')
    <div class="card">
        <div class="card-body">
            <div class="timeline-feature">
                <div class="timeline-feature__bar"></div>
                @foreach ($data as $item)
                    <div class="timeline-feature__item">
                        <span class="timeline-feature__badge"></span>
                        <div class="timeline-feature__content d-flex align-items-center justify-content-between">
                            <span class="mr-3">
                                <span class="timeline-feature__view"
                                      data-id="{{ $item->id }}">
                                    {{ $item->name }}
                                    <span class="badge bg-success" style="cursor: pointer;">Xem</span>
                                </span>
                            </span>
                            <span class="text-right text-danger">{{ $item->created_at}}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <div class="modal fade" id="featureModal" tabindex="-1" aria-labelledby="featureModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="featureModalLabel">Chi tiết tính năng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>
                <div class="modal-body fw-medium" id="feature-content">
                </div>
                <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal">Đóng</button>
            </div>
            </div>
        </div>
    </div>
@endsection

@push('javascript')
    <script>
        $(document).ready(function () {
            $('.timeline-feature__view').on('click', function () {
                let id = $(this).data('id');

                $.ajax({
                    url: `/admin/latest-features/detail/${id}`,
                    type: 'GET',
                    success: function (res) {
                            let html = `<div>${res.data.content}</div>`;
                            if (res.data.image) {
                                html += `<img src="${res.data.image}" alt="" class="img-fluid mt-3">`;
                            }
                            $('#feature-content').html(html);
                            $('#featureModal').modal('show');
                    }
                });
            });
        });

    </script>
@endpush
