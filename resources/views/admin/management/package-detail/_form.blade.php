{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin gói dịch vụ
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="category-id" class="form-label required">Danh mục</label>
                    <select id="category-id" name="category_id" class="form-select">
                        @foreach($categories as $item)
                            <option value="{{ $item->id }}"
                                    @if(isset($dataEdit) && $dataEdit->category_id == $item->id) selected @endif>
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="package-list-id" class="form-label required"><PERSON><PERSON> thứ tự</label>
                    <select id="package-list-id" name="package_list_id" class="form-select">
                        @foreach($packageLists as $item)
                            <option value="{{ $item->id }}"
                                    @if(isset($dataEdit) && $dataEdit->package_list_id == $item->id) selected @endif>
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('package_list_id')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="name" class="form-label required">Tên gói dịch vụ</label>
                    <input type="text" class="form-control" id="name" name="name"
                           value="{{ old('name', $dataEdit->name ?? null) }}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="note" class="form-label required">Mô tả gói dịch vụ</label>
                    <textarea class="form-control" id="note" name="note"
                              rows="3">{{ old('note', $dataEdit->note ?? '') }}</textarea>
                    @error('note')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="mode" class="form-label required">Chế độ</label>
                    <select name="mode" class="form-select" id="mode">
                        @foreach(App\Enums\PackageModeEnum::cases() as $mode)
                            <option value="{{ $mode->value }}"
                                    @if(isset($dataEdit) && $dataEdit->mode == $mode->value) selected @endif>
                                {{ $mode->label() }}
                            </option>
                        @endforeach
                    </select>
                    @error('mode')
                    <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <fieldset class="form-fieldset">
                    <div class="row">

                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="api_provider_id" class="form-label required">Nhà cung cấp</label>
                                <select name="api_provider_id" class="form-select" id="api_provider_id"
                                        data-url="{{ route('manage.providers.get-services') }}"
                                        data-selected="{{ $dataEdit->api_provider_id ?? 0 }}">
                                    <option value="0">Chọn nhà cung cấp</option>
                                    @foreach($providers as $item)
                                        <option value="{{ $item->id }}"
                                                @if(isset($dataEdit) && $dataEdit->provider_id == $item->id) selected @endif>
                                            {{ $item->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('mode')
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                                @enderror
                            </div>
                        </div>
                        <input type="hidden" name="service_name" class="service-name"
                               value="{{ old('service_name', $dataEdit->service_name ?? '') }}">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="service_id" class="form-label">Dịch vụ</label>
                                <select name="service_id"
                                        id="service_id"
                                        class="form-select"
                                        data-selected="{{ old('service_id', $dataEdit->service_id ?? 0) }}"
                                        data-selected-name="{{ old('service_name', $dataEdit->service_name ?? '') }}">
                                    <option value="0">Chọn dịch vụ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="rate" class="form-label">Giá gốc</label>
                                <input class="form-control" type="text" id="rate" name="rate"
                                       value="{{ old('rate', $dataEdit->rate ?? 0) }}">
                            </div>
                            @error('rate')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_type" class="form-label">Định dạng</label>
                                <input class="form-control" type="text" id="service_type" name="service_type"
                                       value="{{ old('service_type', $dataEdit->service_type ?? '') }}">
                            </div>
                            @error('service_type')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="min" class="form-label">Tối thiểu</label>
                                <input class="form-control" type="text" id="min" name="min"
                                       value="{{ old('min', $dataEdit->min ?? 0) }}">
                            </div>
                            @error('min')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max" class="form-label">Tối đa</label>
                                <input class="form-control" type="text" id="max" name="max"
                                       value="{{ old('max', $dataEdit->max ?? 0) }}">
                            </div>
                            @error('max')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-12">
                <fieldset class="form-fieldset">
                    <div class="row">
                        {{-- Bảo hành --}}
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="hidden" name="refill" value="0">
                                <div class="form-check form-switch" dir="ltr">
                                    <input type="checkbox"
                                           class="form-check-input @error('refill') is-invalid @enderror"
                                           id="refill"
                                           name="refill"
                                           value="1"
                                        {{ old('refill', $dataEdit->refill ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="refill">Bảo hành</label>
                                </div>
                                <small>Tích chọn nếu gói dịch vụ có bảo hành</small>
                                @error('refill')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>

                        {{-- Nhỏ giọt --}}
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="hidden" name="drip_feed" value="0">
                                <div class="form-check form-switch" dir="ltr">
                                    <input type="checkbox"
                                           class="form-check-input @error('drip_feed') is-invalid @enderror"
                                           id="drip_feed"
                                           name="drip_feed"
                                           value="1"
                                        {{ old('drip_feed', $dataEdit->drip_feed ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="drip_feed">Nhỏ giọt</label>
                                </div>
                                <small>Tích chọn nếu gói dịch vụ có chế độ chạy nhỏ giọt</small>
                                @error('drip_feed')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="hidden" name="cancel" value="0">
                                <div class="form-check form-switch" dir="ltr">
                                    <input type="checkbox"
                                           class="form-check-input @error('cancel') is-invalid @enderror"
                                           id="cancel"
                                           name="cancel"
                                           value="1"
                                        {{ old('cancel', $dataEdit->cancel ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="cancel">Hủy bỏ</label>
                                </div>
                                <small>Tích chọn nếu gói dịch vụ có thể hủy khi đang thực hiện</small>
                                @error('cancel')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </fieldset>

            </div>
            <div class="col-md-12">
                <fieldset class="form-fieldset">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="member_price" class="form-label required">Giá cập bậc thành viên</label>
                                <input type="text" class="form-control" id="member_price" name="member_price"
                                       value="{{ old('member_price', $dataEdit->member_price ?? null) }}">
                                @error('member_price')
                                <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="collaborator_price" class="form-label required">Giá cập bậc CTV</label>
                                <input type="text" class="form-control" id="collaborator_price"
                                       name="collaborator_price"
                                       value="{{ old('collaborator_price', $dataEdit->collaborator_price ?? null) }}">
                                @error('collaborator_price')
                                <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="agency_price" class="form-label required">Giá cập bậc đại lý</label>
                                <input type="text" class="form-control" id="agency_price" name="agency_price"
                                       value="{{ old('agency_price', $dataEdit->agency_price ?? null) }}">
                                @error('agency_price')
                                <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="distributor_price" class="form-label required">Giá cập bậc nhà phân
                                    phối</label>
                                <input type="text" class="form-control" id="distributor_price" name="distributor_price"
                                       value="{{ old('distributor_price', $dataEdit->distributor_price ?? null) }}">
                                @error('distributor_price')
                                <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-12">
                <fieldset class="form-fieldset">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="status-package" class="form-label required">Trạng thái dịch vụ</label>
                                <select name="status" class="form-select" id="status-package">
                                    @foreach(App\Enums\PackageStatusEnum::cases() as $status)
                                        <option value="{{ $status->value }}"
                                            @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                            {{ $status->label() }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <div class="form-check form-switch" dir="ltr">
                                    <input type="checkbox"
                                           class="form-check-input @error('allow_reaction') is-invalid @enderror"
                                           id="allow_reaction"
                                           name="allow_reaction"
                                           value="1"
                                        {{ old('allow_reaction', $dataEdit->allow_reaction ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="allow_reaction">Chọn biểu cảm</label>
                                </div>
                                <small>Cho phép chọn nhiều cảm xúc tương tác bài viết Facebook</small>
                                @error('allow_reaction')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="hidden" name="visibility" value="0">
                                <div class="form-check form-switch" dir="ltr">
                                    <input type="checkbox"
                                           class="form-check-input @error('visibility') is-invalid @enderror"
                                           id="visibility"
                                           name="visibility"
                                           value="1"
                                        {{ old('visibility', $dataEdit->visibility ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="visibility">Hiển thị</label>
                                </div>
                                <small>Cho phép ẩn hoặc hiển thị gói dịch vụ</small>

                                @error('visibility')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>

        </div>
        <button type="submit" class="btn btn-success btn-submit">
            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2" /><path d="M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M14 4l0 4l-6 0l0 -4" /></svg>
            {{$form_title}}
        </button>
    </div>
</div>
