{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin tính năng cập nhật
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            {{-- Website --}}
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="tenant_id" class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select @error('tenant_id') is-invalid @enderror" id="tenant_id">
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" @selected(old('tenant_id', $dataEdit->tenant_id ?? null) == $tenant->id)>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                    @error('tenant_id')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            {{-- Tên / Tiêu đề --}}
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="name" class="form-label required">Tên/ Tiêu đề</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                           id="name" name="name"
                           value="{{ old('name', $dataEdit->name ?? null) }}">
                    @error('name')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            {{-- Mô tả --}}
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="content" class="form-label required">Mô tả</label>
                    <textarea class="form-control @error('content') is-invalid @enderror"
                              id="content" name="content" rows="3">{{ old('content', $dataEdit->content ?? null) }}</textarea>
                    @error('content')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            {{-- Hình ảnh minh họa --}}
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="image" class="form-label">Hình ảnh minh họa</label>
                    <input type="text" class="form-control @error('image') is-invalid @enderror"
                           id="image" name="image"
                           value="{{ old('image', $dataEdit->image ?? null) }}"
                           oninput="document.getElementById('preview_image').src=this.value || 'https://via.placeholder.com/150';">
                    @error('image')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
                <div class="alert bg-warning text-black fw-medium">
                    Vui lòng lấy đường dẫn ảnh <a href="https://linkanh.xyz/" target="_blank">tại đây</a>
                </div>
                @if (isset($dataEdit->image))
                    <div class="mt-2">
                    <img id="preview_image" src="{{ old('image', $dataEdit->image ?? 'https://placehold.co/150x150/png') }}"
                         alt="Preview" loading="lazy" class="border rounded" style="max-height: 120px;">
                </div>
                @endif
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="pin" class="form-label required">Ghim bài</label>
                    <select name="pin" id="pin" class="form-select @error('pin') is-invalid @enderror">
                        @foreach(\App\Enums\PinHomeNotificationEnum::cases() as $case)
                            <option value="{{ $case->value }}" @selected(old('pin', $dataEdit->pin ?? 0) == $case->value)>
                                {{ $case->label() }}
                            </option>
                        @endforeach
                    </select>
                    @error('pin')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="status" class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select @error('status') is-invalid @enderror">
                        @foreach(\App\Enums\BaseStatusEnum::cases() as $case)
                            <option value="{{ $case->value }}" @selected(old('status', $dataEdit->status ?? 0) == $case->value)>
                                {{ $case->label() }}
                            </option>
                        @endforeach
                    </select>
                    @error('status')
                    <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>
            </div>

        </div>

        {{-- Submit --}}
        <div class="mt-3">
            <button type="submit" class="btn btn-success fw-medium">
            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2" /><path d="M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M14 4l0 4l-6 0l0 -4" /></svg>
                {{ $form_title }}
            </button>
        </div>
    </div>
</div>
