//
// _form-check.scss
//

.form-check {
    position: relative;
    text-align: left
        /*rtl: right*/
    ;

    .form-check-input {
        cursor: pointer;
    }

    label {
        cursor: pointer;
    }
}

.form-check-label {
    cursor: pointer;
    margin-bottom: 0;
}

// checkbox input right
.form-check-right {
    padding-left: 0;
    display: inline-block;

    .form-check-input {
        float: right;
        margin-left: 0;
        margin-right: calc($form-check-padding-start * -1);
    }

    .form-check-label {
        display: block;
    }

    &.form-switch {
        .form-check-input {
            margin-right: calc($form-check-padding-start * -1.6);
        }
    }
}

.form-check-outline {
    .form-check-input {
        position: relative;

        &:checked[type=checkbox] {
            background-image: none;

            &::before {
                content: "\F012C";
                font-family: "Material Design Icons";
                top: -2px;
                position: absolute;
                font-weight: 700;
                font-size: 11px;
                left: 1px;
            }
        }

    }
}

.form-radio-outline {
    .form-check-input {
        position: relative;

        &:checked[type=radio] {
            background-image: none;

            &::before {
                content: "\F0765";
                font-family: "Material Design Icons";
                top: 1px;
                position: absolute;
                font-size: 8px;
                left: 2.7px;
            }
        }

    }
}

// Switch sizes

.form-switch-md {
    padding-left: 2.5rem;
    min-height: 22px;
    line-height: 22px;

    .form-check-input {
        width: 40px;
        height: 20px;
        left: -0.5rem;
        position: relative;
    }

    .form-check-label {
        vertical-align: middle;
    }
}

.form-switch-lg {
    padding-left: 2.75rem;
    min-height: 28px;
    line-height: 28px;

    .form-check-input {
        width: 48px;
        height: 24px;
        left: -0.75rem;
        position: relative;
    }
}

.input-group-text {
    margin-bottom: 0px;
}

// Custom Switch style

.form-switch-custom {
    .form-check-input {
        position: relative;
        background-image: none;

        &::before {
            content: "\F0765";
            font-family: "Material Design Icons";
            top: -8.3px;
            position: absolute;
            font-size: 20px;
            left: -3px;
            color: var(--#{$prefix}secondary-color);
            @include transition($form-switch-transition);
        }

        &:checked {
            background-image: none;
            background-color: $form-check-input-bg !important;
            @include transition($form-switch-transition);

            &::before {
                right: -3px;
                left: auto;
            }
        }

        &:focus {
            background-image: none;
        }
    }
}

// Switch - Right

.form-switch-right {
    display: inline-block;
    padding-right: calc($form-check-padding-start * .5);
    margin-bottom: 0;
    padding-left: 0 !important;

    .form-check-input {
        float: right;
        margin-left: 0;
        margin-right: calc($form-check-padding-start * -1);
        margin-top: .1em !important;
    }

    label {
        margin-bottom: 0;
        margin-right: 1rem;
    }
}

// card radio
.card-radio {
    padding: 0;

    .form-check-label {
        background-color: var(--#{$prefix}secondary-bg);
        border: 1px solid var(--#{$prefix}border-color);
        border-radius: $border-radius;
        padding: 1rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        position: relative;
        padding-right: 32px;

        &:hover {
            cursor: pointer;
        }
    }

    .form-check-input {
        display: none;

        &:checked+.form-check-label {
            border-color: var(--#{$prefix}primary) !important;

            &::before {
                content: "";
                position: absolute;
                top: 2px;
                right: 6px;
                width: 20px;
                height: 20px;
                background-color: var(--#{$prefix}primary);
                border-radius: 50%;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z' clip-rule='evenodd'/%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-size: 60%;
                background-position: center;
            }

        }
    }

    &.dark {
        .form-check-input {
            &:checked+.form-check-label {
                &:before {
                    color: $white;
                }
            }
        }
    }
}

// loop
@each $state in map-keys($theme-colors) {

    // Radio
    .form-radio-outline {
        &.form-radio-#{$state} {
            .form-check-input {
                &:checked[type=radio] {
                    color: var(--#{$prefix}#{$state});
                    background-color: transparent;
                    border-color: var(--#{$prefix}#{$state});
                }
            }
        }
    }

    // checkbox color
    .form-check-#{$state} {
        .form-check-input {
            &:checked {
                background-color: var(--#{$prefix}#{$state});
                border-color: var(--#{$prefix}#{$state});
            }
        }
    }

    .form-radio-#{$state} {
        .form-check-input {
            &:checked {
                border-color: var(--#{$prefix}#{$state});
                background-color: var(--#{$prefix}#{$state});

                &:after {
                    background-color: var(--#{$prefix}#{$state});
                }
            }
        }
    }

    // Checkbox outline
    .form-check-outline {
        &.form-check-#{$state} {
            .form-check-input {
                &:checked[type=checkbox] {
                    color: var(--#{$prefix}#{$state});
                    background-color: transparent;
                    border-color: var(--#{$prefix}#{$state});
                }
            }
        }
    }

    .form-switch-#{$state} {
        .form-check-input {

            &:checked {
                background-color: var(--#{$prefix}#{$state});
                border-color: var(--#{$prefix}#{$state});
            }
        }
    }

    .form-switch-custom {
        &.form-switch-#{$state} {
            .form-check-input {
                &:checked {
                    &::before {
                        color: var(--#{$prefix}#{$state});
                    }

                }
            }
        }
    }
}