/* Bulk Actions Styles */
.bulk-action-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.bulk-action-bar.d-none {
    display: none !important;
}

.bulk-action-bar .selected-count {
    color: #0d6efd;
    font-weight: 600;
    font-size: 1.1em;
}

/* Checkbox Styles */
.row-checkbox, #select-all-checkbox {
    transform: scale(1.2);
    cursor: pointer;
}

.row-checkbox:checked, #select-all-checkbox:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Button Styles */
.bulk-action-btn {
    transition: all 0.2s ease;
    font-weight: 500;
}

.bulk-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-outline-danger.bulk-action-btn:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-outline-success.bulk-action-btn:hover {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

.btn-outline-warning.bulk-action-btn:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-outline-secondary.bulk-action-btn:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* DataTable Checkbox Column */
.dataTables_wrapper .dataTable thead th:first-child,
.dataTables_wrapper .dataTable tbody td:first-child {
    text-align: center;
    vertical-align: middle;
}

/* Animation for bulk action bar */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bulk-action-bar:not(.d-none) {
    animation: slideDown 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bulk-action-bar {
        padding: 10px 15px;
    }
    
    .bulk-action-bar .d-flex {
        flex-direction: column;
        gap: 10px;
    }
    
    .bulk-action-bar .d-flex.align-items-center.gap-2 {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .bulk-action-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* Loading state */
.bulk-action-loading {
    opacity: 0.6;
    pointer-events: none;
}

.bulk-action-loading .bulk-action-btn {
    position: relative;
}

.bulk-action-loading .bulk-action-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Indeterminate checkbox style */
#select-all-checkbox:indeterminate {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#select-all-checkbox:indeterminate::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 2px;
    background: white;
    transform: translate(-50%, -50%);
}

/* Hover effects for table rows */
.dataTables_wrapper .dataTable tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.dataTables_wrapper .dataTable tbody tr:hover .row-checkbox {
    transform: scale(1.3);
}

/* Success/Error message styles */
.bulk-action-message {
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
}

.bulk-action-message.success {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #b8daff;
}

.bulk-action-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
