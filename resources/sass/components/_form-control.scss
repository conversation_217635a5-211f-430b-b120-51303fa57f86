//
// _form-control.scss
//
.form-control{
    color: var(--#{$prefix}form-control-color-text);
    &:focus{
        color: var(--#{$prefix}form-control-color-text);
    }
}
.col-form-label.required:after,.form-label.required:after {
    color: #d63939;
    content: "*";
    margin-left: .25rem
}
.form-icon{
    position: relative;
    .form-control-icon {
        padding-left: calc(#{$input-padding-x} * 3);
        position: relative;
    }
    i {
        position: absolute;
        top: 0px;
        bottom: 0px;
        left: 18px;
        display: flex;
        align-items: center;
    }
    &.right{
        .form-control-icon {
            padding-right: calc(#{$input-padding-x} * 3);
            padding-left: $input-padding-x;
            position: relative;
        }
        i {
            left: auto;
            right: 18px;
        }
    }
}
