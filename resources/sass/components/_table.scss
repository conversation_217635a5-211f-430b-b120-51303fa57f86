//
// _table.scss
//

.table {
    >thead {
        border-color: $table-border-color;
    }
    >:not(:first-child) {
        border-top-width: $table-border-width;
    }
}


.table-nowrap {

    th,
    td {
        white-space: nowrap;
    }
}

// card table

.table-card{
    margin: (-$card-spacer-y) (-$card-spacer-x);

    th,
    td {

        &:first-child{
            padding-left: 16px;
        }

        &:last-child{
            padding-right: 16px;
        }
    }

    .table > :not(:first-child) {
        border-top-width: $table-border-width;
    }
}

@each $state in map-keys($theme-colors) {
    .border-#{$state} {
        &.table {
            > thead {
                border-color: var(--#{$prefix}#{$state}) !important;
            }
        }
    }
    .table-#{$state} {
        &.table {
            > thead {
                border-bottom-color: var(--#{$prefix}#{$state}-border-subtle) !important;
            }
        }
    }
}

.table>:not(caption)>*>* {
    color: var(--#{$prefix}table-color) !important;
    font-weight: 400;
}

.table {
    .form-check {
        padding-left: 0px;
        margin-bottom: 0px;
        .form-check-input {
            margin-left: 0px;
            margin-top: 0px;
            float: none;
            vertical-align: middle;
        }
    }
    .sort {
        position: relative;
        &::before {
            content: "\f035d";
            position: absolute;
            right: 0.5rem;
            top: 18px;
            font-size: 0.8rem;
            font-family: "remixicon";
        }
        &::after {
            position:absolute;
            right: 0.5rem;
            content: "\f0360";
            font-family: "remixicon";
            font-size: 0.8rem;
            top: 12px;
        }
    }
}


.table-vcenter>:not(caption)>*>* {
    vertical-align: middle
}