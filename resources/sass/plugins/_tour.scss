// 
// tour.scss
// 

.shepherd-element{
    background: var(--#{$prefix}secondary-bg);
    box-shadow: $box-shadow;
}

.shepherd-has-title{
    .shepherd-content{
        .shepherd-header{
            background-color: var(--#{$prefix}light);
            padding: $toast-padding-y $toast-padding-x;
        }

        .shepherd-cancel-icon{
            color: rgba(var(--#{$prefix}body-color-rgb), .75);
            &:hover{
                color: rgba(var(--#{$prefix}body-color-rgb), 1);
            }
        }
    }
}

.shepherd-element.shepherd-has-title[data-popper-placement^=bottom]>.shepherd-arrow:before{
    background-color: var(--#{$prefix}light);
}

.shepherd-title{
    font-size: 15px;
    font-weight: $font-weight-medium;
    color: var(--#{$prefix}body-color);
}

.shepherd-text{
    padding: $toast-padding-x;
    font-size: $font-size-base;
    color: var(--#{$prefix}body-color);
}

.shepherd-button{
    &.btn-success:not(:disabled):hover{
        background: var(--#{$prefix}success-text-emphasis);
        color: $white;
    }

    &.btn-light:not(:disabled):hover{
        background: rgba(var(--#{$prefix}light-rgb), .75);
        color: var(--#{$prefix}body-color);
    }

    &.btn-primary:not(:disabled):hover{
        background: var(--#{$prefix}primary-text-emphasis);
        color: $white;
    }
}

.shepherd-footer{
    padding: 0 $toast-padding-x $toast-padding-x;
}
.shepherd-arrow,
.shepherd-arrow:before{
    content: "\ea75";
    font-family: 'remixicon';
    font-size: 24px;
    z-index: 1;
    background-color: transparent !important;
    transform: rotate(0deg);
    color: var(--#{$prefix}primary);
}

.shepherd-element[data-popper-placement^=bottom]>.shepherd-arrow{
    top: -18px;
}

.shepherd-button{
    margin-right: 0.5rem;
}