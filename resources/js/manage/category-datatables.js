"use strict";

import { definedColumns, xAjax, reloadTable, components , bulkActions} from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.platform_name,
        definedColumns.name,
        definedColumns.key,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/categories') + components.btn_delete(row, '/manage/categories');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/categories/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });
bulkActions.init('#datatable-ajax')
});
