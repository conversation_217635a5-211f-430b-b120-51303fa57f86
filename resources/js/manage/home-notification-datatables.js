"use strict";

import { definedColumns, xAjax, reloadTable, components, bulkActions } from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.image,
        definedColumns.name,
        definedColumns.created_at,
        definedColumns.tenant,
        definedColumns.pin,
        definedColumns.status,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/home-notifications') + components.btn_delete(row, '/manage/home-notifications');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/home-notifications/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });

    bulkActions.init('#datatable-ajax');
});
