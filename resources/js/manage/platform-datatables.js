"use strict";

import { definedColumns, xAjax, reloadTable, components, bulkActions } from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.name,
        definedColumns.key,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/platform') + components.btn_delete(row, '/manage/platform');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/platform/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });

    bulkActions.init('#datatable-ajax');
});
