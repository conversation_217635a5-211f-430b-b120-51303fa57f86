"use strict";

import { definedColumns, xAjax, reloadTable, components } from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        // definedColumns.category,
        definedColumns.name,
        definedColumns.member_price,
        definedColumns.collaborator_price,
        definedColumns.agency_price,
        definedColumns.distributor_price,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/package-details') + components.btn_delete(row, '/manage/package-details');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/package-details/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });

});
