"use strict";

import { definedColumns, xAjax, reloadTable, components, bulkActions } from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.name,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/latest-features') + components.btn_delete(row, '/manage/latest-features');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/latest-features/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });

    bulkActions.init('#datatable-ajax');
});
