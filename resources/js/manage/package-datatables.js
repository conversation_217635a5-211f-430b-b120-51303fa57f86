"use strict";

import { definedColumns, xAjax, reloadTable , components} from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.name,
        definedColumns.key,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/packages') + components.btn_delete(row, '/manage/packages');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/packages/ajax/data`),
        order: [[ 0, "desc" ]],
        columns: columns
    });
});
