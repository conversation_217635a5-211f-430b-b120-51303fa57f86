"use strict";

import { definedColumns, xAjax, reloadTable, components, bulkActions } from "../table";
$(document).ready(function() {
    var columns = [
        definedColumns.stt,
        definedColumns.name,
        definedColumns.balance,
        definedColumns.status,
        definedColumns.created_at,
        definedColumns.action((data, type, row) => {
            return components.btn_edit(row, '/manage/providers') + components.btn_delete(row, '/manage/providers');
        }),
    ];

    reloadTable.datatableLog = $('#datatable-ajax').DataTable({
        responsive: false,
        searchDelay: 500,
        processing: true,
        serverSide: true,
        ajax: xAjax(`/manage/providers/ajax/data`),
        order: [[ 1, "desc" ]],
        columns: columns
    });

});
