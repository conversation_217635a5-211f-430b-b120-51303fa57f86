<?php

use App\Http\Controllers\Admin\ActivityLogController;
use App\Http\Controllers\Admin\BalanceChangeController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HomeNotificationController;
use App\Http\Controllers\Admin\LatestFeaturesController;
use App\Http\Controllers\Admin\MemberController;
use App\Http\Controllers\Admin\MemberCreditController;
use App\Http\Controllers\Admin\MemberDebitController;
use App\Http\Controllers\Admin\NoteController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\PolicyAndTermsController;
use App\Http\Controllers\Admin\PriceController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\SupportController;
use App\Http\Controllers\Admin\TenantSettingsController;
use App\Http\Controllers\Admin\TopUpHistoryController;
use App\Http\Controllers\Admin\TransactionController;
use App\Http\Controllers\Admin\UpgradeMemberLevelController;
use Illuminate\Support\Facades\Route;

Route::prefix('admin')
    ->as('admin.')
    ->middleware(['auth', 'require.min.role:1'])
    ->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
        Route::get('price', [PriceController::class, 'index'])->name('price.index');

        Route::prefix('members')
            ->as('members.')
            ->group(function () {
               Route::get('/',[MemberController::class, 'index'])->name('list.index');

               Route::get('credit',[MemberCreditController::class, 'index'])->name('credit.index');

               Route::get('debit',[MemberDebitController::class, 'index'])->name('debit.index');

               Route::get('upgrade-level',[UpgradeMemberLevelController::class, 'index'])->name('upgrade-level.index');
            });

        Route::get('orders',[OrderController::class, 'index'])->name('order.index');

        Route::get('transactions',[TransactionController::class, 'index'])->name('transaction.index');

        Route::get('activity-logs',[ActivityLogController::class, 'index'])->name('activity.index');

        Route::get('top-up',[TopUpHistoryController::class, 'index'])->name('top-up.index');

        Route::get('notes',[NoteController::class, 'index'])->name('note.index');

        Route::get('latest-features',[LatestFeaturesController::class, 'index'])->name('latest-feature.index');
        Route::get('latest-features/detail/{id}',[LatestFeaturesController::class,'detail'])->name('latest-feature.detail');

        Route::get('settings',[SettingController::class, 'index'])->name('settings.index');
        Route::put('settings/update',[SettingController::class, 'update'])->name('settings.update');

        Route::get('supports',[SupportController::class, 'index'])->name('support.index');

        Route::get('policy-term',[PolicyAndTermsController::class, 'index'])->name('policy.index');

        Route::get('home-notification',[HomeNotificationController::class, 'index'])->name('home-notification.index');

        Route::get('balance-change',[BalanceChangeController::class, 'index'])->name('balance-change.index');
    });
