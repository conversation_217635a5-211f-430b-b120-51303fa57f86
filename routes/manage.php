<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Manage\PackageController;
use App\Http\Controllers\Manage\CategoryController;
use App\Http\Controllers\Manage\PlatformController;
use App\Http\Controllers\Manage\ProviderController;
use App\Http\Controllers\Manage\PackageDetailController;
use App\Http\Controllers\Manage\LatestFeaturesController;
use App\Http\Controllers\Manage\HomeNotificationController;

Route::prefix('manage')
    ->as('manage.')
    ->middleware(['auth', 'require.role:2'])
    ->group(function () {
        Route::resource('platform', PlatformController::class)
            ->names('platform');
        Route::get('/platform/ajax/data', [PlatformController::class, 'ajaxData'])->name('platform.ajax.data');

        Route::resource('categories', CategoryController::class)
            ->names('categories');
        Route::get('/categories/ajax/data', [CategoryController::class, 'ajaxData'])->name('category.ajax.data');

        Route::resource('packages', PackageController::class)
            ->names('packages');
        Route::get('/packages/ajax/data', [PackageController::class, 'ajaxData'])->name('package.ajax.data');

        Route::resource('package-details', PackageDetailController::class)
            ->names('package-details');
        Route::get('/package-details/ajax/data', [PackageDetailController::class, 'ajaxData'])->name('package-detail.ajax.data');

        Route::resource('providers', ProviderController::class)
            ->names('providers');
        Route::get('/providers/ajax/data', [ProviderController::class, 'ajaxData'])->name('providers.ajax.data');
        Route::get('/provider/get-services', [ProviderController::class, 'getAllServices'])
            ->name('providers.get-services');

        Route::resource('latest-features', LatestFeaturesController::class)->names('latest-features');
        Route::get('/latest-features/ajax/data', [LatestFeaturesController::class, 'ajaxData'])->name('latest-features.ajax.data');

        Route::resource('home-notifications', HomeNotificationController::class)->names('home-notifications');
        Route::get('/home-notifications/ajax/data', [HomeNotificationController::class, 'ajaxData'])->name('home-notifications.ajax.data');
    });
