<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Enums\BaseStatusEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class SocialMediaMarketingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $platforms = [
            [
                'name' => 'Facebook',
                'description' => 'Mạng xã hội phổ biến để kết nối và chia sẻ nội dung.',
                'status' => 1,
                'key' => 'facebook',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Instagram',
                'description' => 'Nền tảng chia sẻ hình ảnh và video.',
                'status' => 1,
                'key' => 'instagram',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Threads',
                'description' => 'Ứng dụng trò chuyện liên kết với Instagram.',
                'status' => 1,
                'key' => 'threads',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'TikTok',
                'description' => 'Mạng xã hội video ngắn rất phổ biến.',
                'status' => 1,
                'key' => 'tiktok',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Twitter',
                'description' => 'Nền tảng chia sẻ thông tin nhanh chóng (nay là X).',
                'status' => 1,
                'key' => 'twitter',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'YouTube',
                'description' => 'Nền tảng chia sẻ video lớn nhất thế giới.',
                'status' => 1,
                'key' => 'youtube',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Google',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 1,
                'key' => 'google',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Shopee',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 1,
                'key' => 'shopee',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Spotify',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 1,
                'key' => 'spotify',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'LinkedIn',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 1,
                'key' => 'linkedin',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];
        DB::table('platforms')->insert($platforms);

        $categories = [
            // Facebook categories (platform_id = 1)
            [
                'platform_id' => 1,
                'name' => 'Like bài viết Facebook',
                'key' => 'facebook-like-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Bình luận bài viết Facebook',
                'key' => 'facebook-comment-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Lượt thích bình luận bài viết Facebook',
                'key' => 'facebook-like-comment-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Chia sẻ bài viết Facebook',
                'key' => 'facebook-share-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Like/Follow fanpage Facebook',
                'key' => 'facebook-like-follow-fanpage',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Đánh giá fanpage Facebook',
                'key' => 'facebook-review-fanpage',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Theo dõi trang cá nhân Facebook',
                'key' => 'facebook-follow-profile',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Theo thành viên nhóm Facebook',
                'key' => 'facebook-member-group',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'Mắt livestream Facebook',
                'key' => 'facebook-view-livestream',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'View video Facebook',
                'key' => 'facebook-view-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 1,
                'name' => 'View story Facebook',
                'key' => 'facebook-view-story',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            // Instagram categories (platform_id = 2)
            [
                'platform_id' => 2,
                'name' => 'Like bài viết Instagram',
                'key' => 'instagram-like-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 2,
                'name' => 'Bình luận bài viết Instagram',
                'key' => 'instagram-comment-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 2,
                'name' => 'Theo dõi trang cá nhân Instagram',
                'key' => 'instagram-follow-profile',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 2,
                'name' => 'Mắt livestream Instagram',
                'key' => 'instagram-view-livestream',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 2,
                'name' => 'View video Instagram',
                'key' => 'instagram-view-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Threads categories (platform_id = 3)
            [
                'platform_id' => 3,
                'name' => 'Like bài viết Threads',
                'key' => 'threads-like-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 3,
                'name' => 'Bình luận bài viết Threads',
                'key' => 'threads-comment-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 3,
                'name' => 'Theo dõi trang cá nhân Threads',
                'key' => 'threads-follow-profile',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // TikTok categories (platform_id = 4)
            [
                'platform_id' => 4,
                'name' => 'Like video TikTok',
                'key' => 'tiktok-like-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 4,
                'name' => 'Bình luận video TikTok',
                'key' => 'tiktok-comment-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 4,
                'name' => 'Chia sẻ video TikTok',
                'key' => 'tiktok-share-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 4,
                'name' => 'Theo dõi trang cá nhân TikTok',
                'key' => 'tiktok-follow-profile',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 4,
                'name' => 'View video TikTok',
                'key' => 'tiktok-view-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 4,
                'name' => 'Mắt livestream TikTok',
                'key' => 'tiktok-view-livestream',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Twitter categories (platform_id = 5)
            [
                'platform_id' => 5,
                'name' => 'Like tweet Twitter',
                'key' => 'twitter-like-tweet',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 5,
                'name' => 'Retweet Twitter',
                'key' => 'twitter-retweet',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 5,
                'name' => 'Bình luận tweet Twitter',
                'key' => 'twitter-comment-tweet',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 5,
                'name' => 'Theo dõi tài khoản Twitter',
                'key' => 'twitter-follow-account',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // YouTube categories (platform_id = 6)
            [
                'platform_id' => 6,
                'name' => 'Like video YouTube',
                'key' => 'youtube-like-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 6,
                'name' => 'Dislike video YouTube',
                'key' => 'youtube-dislike-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 6,
                'name' => 'Bình luận video YouTube',
                'key' => 'youtube-comment-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 6,
                'name' => 'Subscribe kênh YouTube',
                'key' => 'youtube-subscribe-channel',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 6,
                'name' => 'View video YouTube',
                'key' => 'youtube-view-video',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 6,
                'name' => 'Mắt livestream YouTube',
                'key' => 'youtube-view-livestream',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Google categories (platform_id = 7)
            [
                'platform_id' => 7,
                'name' => 'Đánh giá Google Maps',
                'key' => 'google-review-maps',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 7,
                'name' => 'Đánh giá Google Business',
                'key' => 'google-review-business',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Shopee categories (platform_id = 8)
            [
                'platform_id' => 8,
                'name' => 'Đánh giá sản phẩm Shopee',
                'key' => 'shopee-review-product',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 8,
                'name' => 'Theo dõi shop Shopee',
                'key' => 'shopee-follow-shop',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 8,
                'name' => 'Like sản phẩm Shopee',
                'key' => 'shopee-like-product',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Spotify categories (platform_id = 9)
            [
                'platform_id' => 9,
                'name' => 'Theo dõi nghệ sĩ Spotify',
                'key' => 'spotify-follow-artist',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 9,
                'name' => 'Like bài hát Spotify',
                'key' => 'spotify-like-song',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 9,
                'name' => 'Nghe nhạc Spotify',
                'key' => 'spotify-play-music',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // LinkedIn categories (platform_id = 10)
            [
                'platform_id' => 10,
                'name' => 'Like bài viết LinkedIn',
                'key' => 'linkedin-like-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 10,
                'name' => 'Bình luận bài viết LinkedIn',
                'key' => 'linkedin-comment-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 10,
                'name' => 'Chia sẻ bài viết LinkedIn',
                'key' => 'linkedin-share-post',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 10,
                'name' => 'Kết nối LinkedIn',
                'key' => 'linkedin-connect-profile',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'platform_id' => 10,
                'name' => 'Theo dõi trang công ty LinkedIn',
                'key' => 'linkedin-follow-company',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        DB::table('categories')->insert($categories);

        $packageList = [
            [
                'name' => 'Gói 1',
                'key' => 'package-1',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 2',
                'key' => 'package-2',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 3',
                'key' => 'package-3',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 4',
                'key' => 'package-4',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 5',
                'key' => 'package-5',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 6',
                'key' => 'package-6',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 7',
                'key' => 'package-7',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 8',
                'key' => 'package-8',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 9',
                'key' => 'package-9',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Gói 10',
                'key' => 'package-10',
                'status' => BaseStatusEnum::ACTIVE->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        DB::table('packages')->insert($packageList);
    }
}
