<?php

namespace Database\Seeders;

use App\Enums\UserRoleEnum;
use App\Models\Tenant;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $tenant = Tenant::create([
            'domain' => 'main-seed.test',
            'status' => 1,
        ]);

        // Tạo User mặc định
        $user = User::create([
            'username'         => 'vannguyentrung',
            'name'             => 'Van Nguyen Trung',
            'email'            => '<EMAIL>',
            'password'         => Hash::make('password'),
            'gender'           => 0,
            'status'           => 1,
            'membership_level' => 0,
        ]);

        $token = $user->createToken('default')->plainTextToken;
        $user->update(['api_key' => $token]);

        $tenant->users()->attach($user->id, [
            'role'           => UserRoleEnum::MANAGE->value,
            'balance'        => 0,
            'total_deposit'  => 0,
            'total_spent'    => 0,
            'created_at'     => now(),
            'updated_at'     => now(),
        ]);

        $this->call(SocialMediaMarketingSeeder::class);
    }
}
