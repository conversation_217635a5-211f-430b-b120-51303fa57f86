<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\TenantSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class TenantSettingsService
{
    /**
     * Get all settings for a tenant with defaults merged
     */
    public function getAllSettings(int $tenantId): array
    {
        $defaults = $this->getDefaultSettings();
        $tenantSettings = TenantSetting::getAllForTenant($tenantId);
        
        return array_merge($defaults, $tenantSettings);
    }

    /**
     * Get a specific setting value for a tenant
     */
    public function getSetting(int $tenantId, string $key, $default = null)
    {
        $value = TenantSetting::getValue($tenantId, $key);
        
        if ($value === null) {
            $defaults = $this->getDefaultSettings();
            return $defaults[$key] ?? $default;
        }
        
        return $value;
    }

    /**
     * Set a setting value for a tenant with validation
     */
    public function setSetting(int $tenantId, string $key, $value): TenantSetting
    {
        $this->validateSetting($key, $value);
        
        return TenantSetting::setValue($tenantId, $key, $value);
    }

    /**
     * Set multiple settings for a tenant
     */
    public function setMultipleSettings(int $tenantId, array $settings): array
    {
        $results = [];
        
        foreach ($settings as $key => $value) {
            $results[$key] = $this->setSetting($tenantId, $key, $value);
        }
        
        return $results;
    }

    /**
     * Reset a setting to its default value
     */
    public function resetSetting(int $tenantId, string $key): bool
    {
        $setting = TenantSetting::forTenant($tenantId)->byKey($key)->first();
        
        if ($setting) {
            $setting->delete();
            return true;
        }
        
        return false;
    }

    /**
     * Reset all settings for a tenant to defaults
     */
    public function resetAllSettings(int $tenantId): bool
    {
        $deleted = TenantSetting::forTenant($tenantId)->delete();
        
        // Clear cache
        Cache::forget("tenant_settings:{$tenantId}");
        
        return $deleted > 0;
    }

    /**
     * Get default settings configuration
     */
    public function getDefaultSettings(): array
    {
        return config('tenant-settings.defaults', []);
    }

    /**
     * Get setting validation rules
     */
    public function getValidationRules(): array
    {
        return config('tenant-settings.validation', []);
    }

    /**
     * Get setting types configuration
     */
    public function getSettingTypes(): array
    {
        return config('tenant-settings.types', []);
    }

    /**
     * Validate a setting key and value
     */
    protected function validateSetting(string $key, $value): void
    {
        $rules = $this->getValidationRules();
        
        if (!isset($rules[$key])) {
            throw new \InvalidArgumentException("Unknown setting key: {$key}");
        }
        
        $validator = Validator::make(
            [$key => $value],
            [$key => $rules[$key]]
        );
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Get settings grouped by category
     */
    public function getSettingsByCategory(int $tenantId): array
    {
        $allSettings = $this->getAllSettings($tenantId);
        $categories = config('tenant-settings.categories', []);
        $grouped = [];
        
        foreach ($categories as $category => $keys) {
            $grouped[$category] = [];
            foreach ($keys as $key) {
                if (isset($allSettings[$key])) {
                    $grouped[$category][$key] = $allSettings[$key];
                }
            }
        }
        
        return $grouped;
    }

    /**
     * Check if a tenant exists and is active
     */
    public function validateTenant(int $tenantId): Tenant
    {
        $tenant = Tenant::find($tenantId);
        
        if (!$tenant) {
            throw new \InvalidArgumentException("Tenant not found: {$tenantId}");
        }
        
        if (!$tenant->isActive()) {
            throw new \InvalidArgumentException("Tenant is not active: {$tenantId}");
        }
        
        return $tenant;
    }

    /**
     * Get setting metadata (type, description, etc.)
     */
    public function getSettingMetadata(string $key): array
    {
        $metadata = config('tenant-settings.metadata', []);
        return $metadata[$key] ?? [];
    }

    /**
     * Get all available setting keys
     */
    public function getAvailableSettings(): array
    {
        return array_keys($this->getDefaultSettings());
    }

    /**
     * Export tenant settings as JSON
     */
    public function exportSettings(int $tenantId): string
    {
        $settings = $this->getAllSettings($tenantId);
        return json_encode($settings, JSON_PRETTY_PRINT);
    }

    /**
     * Import tenant settings from array
     */
    public function importSettings(int $tenantId, array $settings): array
    {
        $this->validateTenant($tenantId);
        
        $results = [];
        foreach ($settings as $key => $value) {
            try {
                $results[$key] = $this->setSetting($tenantId, $key, $value);
            } catch (\Exception $e) {
                $results[$key] = ['error' => $e->getMessage()];
            }
        }
        
        return $results;
    }

    /**
     * Clear all cached settings for a tenant
     */
    public function clearCache(int $tenantId): void
    {
        $settings = TenantSetting::forTenant($tenantId)->get();
        
        foreach ($settings as $setting) {
            Cache::forget("tenant_setting:{$tenantId}:{$setting->key}");
        }
        
        Cache::forget("tenant_settings:{$tenantId}");
    }
}
