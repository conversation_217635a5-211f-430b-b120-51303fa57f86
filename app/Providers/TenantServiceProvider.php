<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class TenantServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('tenant', function () {
            return null;
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        config([
            'tenant.fallback_domain' => env('TENANT_FALLBACK_DOMAIN'),
            'tenant.cache_ttl' => env('TENANT_CACHE_TTL', 21600),
        ]);
    }
}
