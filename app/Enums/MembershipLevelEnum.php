<?php

namespace App\Enums;

enum MembershipLevelEnum: int
{
    case MEMBER = 0;

    case COLLABORATOR = 1;

    case AGENCY = 2;

    case DISTRIBUTOR = 3;

    public function label(): string
    {
        return match ($this) {
            self::MEMBER => 'Thành viên',
            self::COLLABORATOR => 'Cộng tác viên',
            self::AGENCY => 'Đại lý',
            self::DISTRIBUTOR => 'Nhà phân phối'
        };
    }
}
