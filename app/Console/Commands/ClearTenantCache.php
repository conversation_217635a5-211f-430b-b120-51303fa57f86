<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearTenantCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:clear-cache {domain?}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear tenant cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $domain = $this->argument('domain');

        if ($domain) {
            Cache::forget("tenant_domain:{$domain}");
            $this->info("Cache cleared for domain: {$domain}");
        } else {
            Cache::forget('tenant_*');
            $this->info('All tenant cache cleared');
        }
    }
}
