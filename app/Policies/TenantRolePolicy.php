<?php

namespace App\Policies;

use App\Enums\UserRoleEnum;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TenantRolePolicy
{
    use HandlesAuthorization;

    /**
     * Check if user can access admin features
     */
    public function accessAdmin(User $user): bool
    {
        return $user->canAccessAdmin();
    }

    /**
     * Check if user can access manager features
     */
    public function accessManager(User $user): bool
    {
        return $user->canAccessManager();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(User $user, UserRoleEnum $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Check if user has minimum role level
     */
    public function hasMinRole(User $user, UserRoleEnum $minRole): bool
    {
        return $user->hasMinRole($minRole);
    }

    /**
     * Check if user can manage other users
     */
    public function manageUsers(User $user): bool
    {
        return $user->hasMinRole(UserRoleEnum::ADMIN);
    }

    /**
     * Check if user can manage tenants
     */
    public function manageTenants(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can view system settings
     */
    public function viewSystemSettings(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can modify system settings
     */
    public function modifySystemSettings(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can view financial reports
     */
    public function viewFinancialReports(User $user): bool
    {
        return $user->hasMinRole(UserRoleEnum::ADMIN);
    }

    /**
     * Check if user can view advanced reports
     */
    public function viewAdvancedReports(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can manage user roles
     */
    public function manageUserRoles(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can delete users
     */
    public function deleteUsers(User $user): bool
    {
        return $user->hasRole(UserRoleEnum::MANAGE);
    }

    /**
     * Check if user can view activity logs
     */
    public function viewActivityLogs(User $user): bool
    {
        return $user->hasMinRole(UserRoleEnum::ADMIN);
    }

    /**
     * Check if user can export data
     */
    public function exportData(User $user): bool
    {
        return $user->hasMinRole(UserRoleEnum::ADMIN);
    }
}
