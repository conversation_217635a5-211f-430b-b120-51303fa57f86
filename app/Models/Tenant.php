<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

class Tenant extends Model
{
    use HasFactory;

    protected $table = 'tenants';
    protected $fillable = [
        'domain',
        'description',
        'data',
        'status',
        'pin_code',
        'is_main',
    ];

    protected $casts = [
        'data' => 'array',
        'is_main' => 'boolean',
        'status' => BaseStatusEnum::class,
    ];

    protected $with = [];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'tenant_users')
            ->withPivot(['phone', 'role', 'balance', 'total_deposit', 'total_spent'])
            ->withTimestamps();
    }

    public function settings(): HasMany
    {
        return $this->hasMany(TenantSetting::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', BaseStatusEnum::ACTIVE);
    }

    public function isActive(): bool
    {
        return $this->status === BaseStatusEnum::ACTIVE;
    }

    public function getStatusLabel(): string
    {
        return $this->status->label();
    }

    protected static function booted(): void
    {
        static::updated(function ($tenant) {
            Cache::forget("tenant_domain:{$tenant->domain}");
            if ($tenant->wasChanged('domain')) {
                Cache::forget("tenant_domain:{$tenant->getOriginal('domain')}");
            }
        });

        static::deleted(function ($tenant) {
            Cache::forget("tenant_domain:{$tenant->domain}");
        });
    }

    public function getSetting($key, $default = null)
    {
        $setting = $this->settings()->where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }
    
    public function setSetting($key, $value)
    {
        return $this->settings()->updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );
    }

    public function homeNotifications(): HasMany
    {
        return $this->hasMany(HomeNotification::class);
    }
}
