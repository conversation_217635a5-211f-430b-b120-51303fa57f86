<?php

namespace App\Models;

use App\Enums\PackageModeEnum;
use App\Enums\PackageStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PackageDetail extends Model
{
    protected $table = 'package_details';

    protected $fillable = [
        'name',
        'tenant_id',
        'api_provider_id',
        'service_id',
        'package_id',
        'category_id',
        'description',
        'mode',
        'price',
        'member_price',
        'collaborator_price',
        'agency_price',
        'distributor_price',
        'min',
        'max',
        'refill',
        'refill_type',
        'cancel',
        'service_type',
        'drip_feed',
        'deny_duplicates',
        'note',
        'allow_reaction',
        'status',
        'visibility',
        'service_name'
    ];
    protected $casts = [
        'rate' => 'decimal:2',
        'member_price' => 'decimal:2',
        'collaborator_price' => 'decimal:2',
        'agency_price' => 'decimal:2',
        'distributor_price' => 'decimal:2',
        'refill' => 'boolean',
        'cancel' => 'boolean',
        'deny_duplicates' => 'boolean',
        'allow_reaction' => 'boolean',
        'visibility' => 'boolean',
        'status' => PackageStatusEnum::class,
        'mode' => PackageModeEnum::class
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class, 'api_provider_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
