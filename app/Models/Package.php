<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    protected $table = 'packages';

    protected $fillable = [
        'name',
        'key',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class
    ];

    public function packageDetails(): HasMany
    {
        return $this->hasMany(PackageDetail::class);
    }
}
