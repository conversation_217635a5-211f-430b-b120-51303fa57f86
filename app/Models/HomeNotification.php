<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use App\Enums\PinHomeNotificationEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HomeNotification extends Model
{
    protected $table = 'home_notifications';

    protected $fillable = [
        'tenant_id',
        'name',
        'content',
        'image',
        'pin',
        'status'
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'pin' => PinHomeNotificationEnum::class
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }
}
