<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\BaseStatusEnum;
use App\Enums\MembershipLevelEnum;
use App\Enums\UserRoleEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'gender',
        'dob',
        'api_key',
        'status',
        'membership_level',
        'avatar'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'membership_level' => MembershipLevelEnum::class,
        'status' => BaseStatusEnum::class
    ];


    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class, 'tenant_users')
            ->withPivot('role', 'phone', 'balance', 'total_deposit', 'total_spent')
            ->withTimestamps();
    }

    public function activityLogs(): HasMany
    {
        return $this->hasMany(UserActivityLog::class);
    }

    /**
     * Get user's role within the current tenant context
     */
    public function getTenantRole(?int $tenantId = null): ?UserRoleEnum
    {
        $tenantId = $tenantId ?? tenant_id();

        if (!$tenantId) {
            return null;
        }

        $tenantUser = $this->tenants()
            ->where('tenant_id', $tenantId)
            ->first();

        if (!$tenantUser) {
            return null;
        }

        return UserRoleEnum::tryFrom($tenantUser->pivot->role);
    }

    /**
     * Check if user has a specific role within the current tenant
     */
    public function hasRole(UserRoleEnum $role, ?int $tenantId = null): bool
    {
        $userRole = $this->getTenantRole($tenantId);
        return $userRole === $role;
    }

    /**
     * Check if user has any of the specified roles within the current tenant
     */
    public function hasAnyRole(array $roles, ?int $tenantId = null): bool
    {
        $userRole = $this->getTenantRole($tenantId);

        if (!$userRole) {
            return false;
        }

        return in_array($userRole, $roles);
    }

    /**
     * Check if user has minimum required role level within the current tenant
     * Role hierarchy: USER (0) < ADMIN (1) < MANAGER (2)
     */
    public function hasMinRole(UserRoleEnum $minRole, ?int $tenantId = null): bool
    {
        $userRole = $this->getTenantRole($tenantId);

        if (!$userRole) {
            return false;
        }

        return $userRole->value >= $minRole->value;
    }

    /**
     * Check if user is a manager within the current tenant
     */
    public function isManager(?int $tenantId = null): bool
    {
        return $this->hasRole(UserRoleEnum::MANAGE, $tenantId);
    }

    /**
     * Check if user is an admin within the current tenant
     */
    public function isAdmin(?int $tenantId = null): bool
    {
        return $this->hasRole(UserRoleEnum::ADMIN, $tenantId);
    }

    /**
     * Check if user is a regular user within the current tenant
     */
    public function isUser(?int $tenantId = null): bool
    {
        return $this->hasRole(UserRoleEnum::USER, $tenantId);
    }

    /**
     * Check if user can access admin features (admin or manager)
     */
    public function canAccessAdmin(?int $tenantId = null): bool
    {
        return $this->hasMinRole(UserRoleEnum::ADMIN, $tenantId);
    }

    /**
     * Check if user can access manager features (manager only)
     */
    public function canAccessManager(?int $tenantId = null): bool
    {
        return $this->hasRole(UserRoleEnum::MANAGE, $tenantId);
    }

    /**
     * Get user's role label within the current tenant
     */
    public function getTenantRoleLabel(?int $tenantId = null): ?string
    {
        $role = $this->getTenantRole($tenantId);
        return $role?->labels();
    }

    /**
     * Check if user belongs to the current tenant
     */
    public function belongsToTenant(?int $tenantId = null): bool
    {
        $tenantId = $tenantId ?? tenant_id();

        if (!$tenantId) {
            return false;
        }

        return $this->tenants()->where('tenant_id', $tenantId)->exists();
    }
}
