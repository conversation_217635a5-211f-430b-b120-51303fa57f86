<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class TenantSetting extends Model
{
    protected $table = 'tenant_settings';

    protected $fillable = [
        'tenant_id',
        'key',
        'value'
    ];

    protected $casts = [
        'tenant_id' => 'integer',
    ];

    /**
     * Get the tenant that owns the setting
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Clear cache when model is updated or deleted
     */
    protected static function booted(): void
    {
        static::saved(function ($setting) {
            Cache::forget("tenant_setting:{$setting->tenant_id}:{$setting->key}");
            Cache::forget("tenant_settings:{$setting->tenant_id}");
        });

        static::deleted(function ($setting) {
            Cache::forget("tenant_setting:{$setting->tenant_id}:{$setting->key}");
            Cache::forget("tenant_settings:{$setting->tenant_id}");
        });
    }
}
