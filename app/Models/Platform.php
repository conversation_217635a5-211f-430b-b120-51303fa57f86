<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;

class Platform extends Model
{
    protected $table = 'platforms';

    protected $fillable = [
        'name',
        'description',
        'status',
        'key',
    ];

    protected $casts = [
        'status'=> BaseStatusEnum::class
    ];
//    public function categories(): HasMany
//    {
//        return $this->hasMany(Category::class, 'platform_id');
//    }

}
