<?php

namespace App\Models;

use App\Enums\BaseStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Provider extends Model
{
    protected $table = 'providers';

    protected $fillable = [
        'name',
        'key',
        'url',
        'currency',
        'type',
        'balance',
        'exchange_rate',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class
    ];

    public function packageDetails(): HasMany
    {
        return $this->hasMany(PackageDetail::class, 'api_provider_id');
    }
}
