<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TenantSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return false;
        }

        // Check if user belongs to current tenant
        if (!user_belongs_to_tenant()) {
            return false;
        }

        // Check if user has admin privileges
        return user_can_access_admin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [];
        $settings = $this->input('settings', []);
        $validationRules = config('tenant-settings.validation', []);

        // Build validation rules for each setting being updated
        foreach ($settings as $key => $value) {
            if (isset($validationRules[$key])) {
                $rules["settings.{$key}"] = $validationRules[$key];
            }
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        $attributes = [];
        $settings = $this->input('settings', []);
        $metadata = config('tenant-settings.metadata', []);

        foreach ($settings as $key => $value) {
            if (isset($metadata[$key]['label'])) {
                $attributes["settings.{$key}"] = $metadata[$key]['label'];
            }
        }

        return $attributes;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'settings.*.required' => ':attribute là bắt buộc.',
            'settings.*.string' => ':attribute phải là chuỗi ký tự.',
            'settings.*.numeric' => ':attribute phải là số.',
            'settings.*.integer' => ':attribute phải là số nguyên.',
            'settings.*.boolean' => ':attribute phải là true hoặc false.',
            'settings.*.email' => ':attribute phải là địa chỉ email hợp lệ.',
            'settings.*.url' => ':attribute phải là URL hợp lệ.',
            'settings.*.max' => ':attribute không được vượt quá :max ký tự.',
            'settings.*.min' => ':attribute phải có ít nhất :min ký tự.',
            'settings.*.in' => ':attribute không hợp lệ.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $settings = $this->input('settings', []);
        $types = config('tenant-settings.types', []);

        // Process settings based on their types
        foreach ($settings as $key => $value) {
            $type = $types[$key] ?? 'text';

            switch ($type) {
                case 'boolean':
                    // Convert various boolean representations to actual boolean
                    $settings[$key] = in_array($value, ['1', 'on', 'true', true, 1], true);
                    break;

                case 'number':
                case 'integer':
                    // Convert to numeric if not empty
                    if ($value !== '' && $value !== null) {
                        $settings[$key] = is_numeric($value) ? ($type === 'integer' ? (int) $value : (float) $value) : $value;
                    }
                    break;

                case 'text':
                case 'textarea':
                case 'email':
                case 'url':
                case 'password':
                case 'select':
                default:
                    // Keep as string, trim whitespace
                    $settings[$key] = is_string($value) ? trim($value) : $value;
                    break;
            }
        }

        $this->merge(['settings' => $settings]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        if ($this->expectsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);

            throw new \Illuminate\Validation\ValidationException($validator, $response);
        }

        parent::failedValidation($validator);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $validator->after(function ($validator) {
            $settings = $this->input('settings', []);
            
            // Custom validation logic
            $this->validateDepositLevels($validator, $settings);
            $this->validateBankingSettings($validator, $settings);
            $this->validateOrderAmounts($validator, $settings);
        });
    }

    /**
     * Validate deposit levels are in ascending order.
     */
    protected function validateDepositLevels(\Illuminate\Contracts\Validation\Validator $validator, array $settings): void
    {
        $levels = [
            'deposit_level_member' => $settings['deposit_level_member'] ?? 0,
            'deposit_level_collaborator' => $settings['deposit_level_collaborator'] ?? 0,
            'deposit_level_agency' => $settings['deposit_level_agency'] ?? 0,
            'deposit_level_distributor' => $settings['deposit_level_distributor'] ?? 0,
        ];

        // Convert to numeric for comparison
        foreach ($levels as $key => $value) {
            $levels[$key] = is_numeric($value) ? (float) $value : 0;
        }

        // Check if levels are in ascending order
        $levelValues = array_values($levels);
        for ($i = 1; $i < count($levelValues); $i++) {
            if ($levelValues[$i] < $levelValues[$i - 1]) {
                $validator->errors()->add('settings.deposit_levels', 'Các mức nạp tiền phải tăng dần theo cấp độ thành viên.');
                break;
            }
        }
    }

    /**
     * Validate banking settings consistency.
     */
    protected function validateBankingSettings(\Illuminate\Contracts\Validation\Validator $validator, array $settings): void
    {
        // If ACB is enabled, required fields must be filled
        if (!empty($settings['acb_public']) && $settings['acb_public']) {
            $requiredAcbFields = ['acb_account_name', 'acb_account_number'];
            foreach ($requiredAcbFields as $field) {
                if (empty($settings[$field])) {
                    $validator->errors()->add("settings.{$field}", 'Trường này là bắt buộc khi sử dụng ngân hàng ACB.');
                }
            }
        }

        // If Vietcombank is enabled, required fields must be filled
        if (!empty($settings['vietcombank_public']) && $settings['vietcombank_public']) {
            $requiredVcbFields = ['vietcombank_account_username', 'vietcombank_account_number'];
            foreach ($requiredVcbFields as $field) {
                if (empty($settings[$field])) {
                    $validator->errors()->add("settings.{$field}", 'Trường này là bắt buộc khi sử dụng ngân hàng Vietcombank.');
                }
            }
        }
    }

    /**
     * Validate order amount settings.
     */
    protected function validateOrderAmounts(\Illuminate\Contracts\Validation\Validator $validator, array $settings): void
    {
        $minAmount = isset($settings['min_order_amount']) ? (float) $settings['min_order_amount'] : 0;
        $maxAmount = isset($settings['max_order_amount']) ? (float) $settings['max_order_amount'] : 0;

        if ($minAmount > 0 && $maxAmount > 0 && $minAmount >= $maxAmount) {
            $validator->errors()->add('settings.max_order_amount', 'Số tiền đơn hàng tối đa phải lớn hơn số tiền tối thiểu.');
        }
    }
}
