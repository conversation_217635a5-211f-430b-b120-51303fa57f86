<?php

namespace App\Http\Requests\Manage;

use Illuminate\Foundation\Http\FormRequest;

class PackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'api_provider_id' => ['required', 'integer'],
            'service_id' => ['required'],
            'package_list_id' => ['required', 'integer'],
            'category_id' => ['required', 'integer'],
            'description' => ['nullable', 'string'],
            'mode' => ['required', 'string'],
            'rate' => ['required',],
            'member_price' => ['required',],
            'collaborator_price' => ['required',],
            'agency_price' => ['required',],
            'distributor_price' => ['required',],
            'min' => ['required', 'integer'],
            'max' => ['required', 'integer'],
            'refill' => ['boolean'],
            'drip_feed' => ['boolean'],
            'cancel' => ['boolean'],
            'service_type' => ['required', 'string'],
            'refill_type' => ['string'],
            'allow_reaction' => ['boolean'],
            'status' => 'required',
            'visibility' => 'boolean',
            'note' => ['required', 'string'],
            'service_name' => [ 'string'],
        ];
    }
}
