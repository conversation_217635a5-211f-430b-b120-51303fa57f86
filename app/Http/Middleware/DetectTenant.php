<?php

namespace App\Http\Middleware;

use App\Enums\BaseStatusEnum;
use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class DetectTenant
{
    public function handle(Request $request, Closure $next): Response
    {
        $domain = $this->extractDomain($request);

        $tenant = $this->getTenantByDomain($domain);

        if (!$tenant || !$tenant->isActive()) {
            return $this->handleTenantNotFound($domain);
        }

        $this->setTenantContext($tenant);

        return $next($request);
    }

    private function extractDomain(Request $request): string
    {
        $host = $request->getHost();

        if (str_contains($host, ':')) {
            $host = explode(':', $host)[0];
        }

        return strtolower(ltrim($host, 'www.'));
    }

    private function getTenantByDomain(string $domain): ?Tenant
    {
        return Cache::remember(
            "tenant_domain:{$domain}",
            now()->addHours(6),
            fn() => Tenant::select(['id', 'domain', 'description', 'status', 'data'])
                ->where('domain', $domain)
                ->where('status', BaseStatusEnum::ACTIVE)
                ->first()
        );
    }

    private function setTenantContext(Tenant $tenant): void
    {
        app()->singleton('tenant', fn() => $tenant);

        config([
            'tenant.id' => $tenant->id,
            'tenant.domain' => $tenant->domain,
        ]);
    }

    private function handleTenantNotFound(string $domain): Response
    {
        if (app()->environment('local')) {
            logger("Tenant not found for domain: {$domain}");
        }

        if (config('tenant.fallback_domain')) {
            return redirect()->to(config('tenant.fallback_domain'));
        }

        abort(404, 'Domain not found');
    }
}
