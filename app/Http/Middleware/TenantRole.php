<?php

namespace App\Http\Middleware;

use App\Enums\UserRoleEnum;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TenantRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = tenant_id();

        if (!$tenantId) {
            abort(404, 'Tenant not found');
        }

        if (!$user->belongsToTenant($tenantId)) {
            abort(403, 'Access denied: You do not belong to this tenant');
        }

        // Convert string roles to UserRoleEnum instances
        $requiredRoles = array_map(function ($role) {
            return UserRoleEnum::tryFrom((int) $role) ?? UserRoleEnum::tryFrom($role);
        }, $roles);

        $requiredRoles = array_filter($requiredRoles);

        if (empty($requiredRoles)) {
            abort(500, 'Invalid role configuration');
        }

        if (!$user->hasAnyRole($requiredRoles, $tenantId)) {
            abort(403, 'Access denied: Insufficient permissions');
        }

        return $next($request);
    }
}
