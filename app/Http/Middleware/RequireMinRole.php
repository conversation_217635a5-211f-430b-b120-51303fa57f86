<?php

namespace App\Http\Middleware;

use App\Enums\UserRoleEnum;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RequireMinRole
{
    /**
     * Handle an incoming request.
     * Requires user to have minimum role level (role hierarchy: USER < ADMIN < MANAGER).
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $minRole): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = tenant_id();

        if (!$tenantId) {
            abort(404, 'Tenant not found');
        }

        if (!$user->belongsToTenant($tenantId)) {
            abort(403, 'Access denied: You do not belong to this tenant');
        }

        $requiredMinRole = UserRoleEnum::tryFrom((int) $minRole) ?? UserRoleEnum::tryFrom($minRole);

        if (!$requiredMinRole) {
            abort(500, 'Invalid role configuration');
        }

        if (!$user->hasMinRole($requiredMinRole, $tenantId)) {
            abort(403, 'Access denied: Insufficient permissions');
        }

        return $next($request);
    }
}
