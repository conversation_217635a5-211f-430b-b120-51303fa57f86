<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use App\Models\TenantUser;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the manager dashboard.
     */
    public function index(Request $request): View
    {
        // Ensure user is a manager
        if (!user_is_manager()) {
            abort(403, 'Access denied: Manager privileges required');
        }

        // Get statistics for the dashboard
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::active()->count(),
            'total_users' => User::count(),
            'total_tenant_users' => TenantUser::count(),
            'managers_count' => TenantUser::where('role', 2)->count(),
            'admins_count' => TenantUser::where('role', 1)->count(),
            'users_count' => TenantUser::where('role', 0)->count(),
        ];

        // Get recent tenants
        $recent_tenants = Tenant::latest()
            ->take(5)
            ->get();

        // Get recent users
        $recent_users = User::latest()
            ->take(5)
            ->get();

        return view('manager.dashboard.index', compact('stats', 'recent_tenants', 'recent_users'));
    }
}
