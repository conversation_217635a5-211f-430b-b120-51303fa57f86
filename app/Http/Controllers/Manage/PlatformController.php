<?php

namespace App\Http\Controllers\Manage;

use App\Http\Controllers\Controller;
use App\Models\Platform;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PlatformController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/manage/platform-datatables.js']);
        return view('admin.management.platforms.index');
    }

    public function ajaxData()
    {
        $data = Platform::query()
            ->select('id', 'name', 'key', 'created_at', 'status');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'key', 'created_at', 'status'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.management.platforms.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|int',
            'key' => 'required|unique:platforms|string|max:255',
        ]);

        Platform::query()->create([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'status' => $validated['status'] ?? 'published',
            'key' => $validated['key'] ?? null,
        ]);

        return $this->httpResponse()->setNextRoute('manage.platform.index')->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $platform = Platform::query()->findOrFail($id);
        return view('admin.management.platforms.edit', [
            'dataEdit' => $platform,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string|int $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|int',
            'key' => 'required|unique:platforms,key,' . $id,
        ]);
        $platform = Platform::query()->findOrFail($id);
        $platform->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'status' => $validated['status'] ?? $platform->status,
            'key' => $validated['key'],
        ]);

        return $this->httpResponse()->setNextRoute('manage.platform.index')->withUpdatedSuccessMessage();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string|int $id)
    {
        $query = Platform::query()->findOrFail($id);
        $query->delete();

        return $this->httpResponse()->setNextRoute('manage.platform.index')->withDeletedSuccessMessage();
    }

}
