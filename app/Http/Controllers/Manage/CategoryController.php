<?php

namespace App\Http\Controllers\Manage;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Platform;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/manage/category-datatables.min.js']);
        return view('admin.management.categories.index');
    }
    public function ajaxData()
    {
        $data = Category::query()
            ->select('id', 'name', 'platform_id','key', 'created_at', 'status');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'platform_id', 'key', 'created_at', 'status'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->addColumn('platform_name', function ($row) {
                return $row->platform->name;
            })
            ->orderColumn('platform_name', function ($query, $order) {
                $query->join('platforms', 'categories.platform_id', '=', 'platforms.id')
                    ->orderBy('platforms.name', $order);
            })
            ->make(true);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $platforms = Platform::query()->select('id', 'name', 'status')->where('status', BaseStatusEnum::ACTIVE->value)->get();
        return view('admin.management.categories.create', [
            'platforms' => $platforms,
        ]);

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'platform_id' => 'nullable|exists:platforms,id',
            'description' => 'nullable|string',
            'key' => 'required|string|max:255|unique:categories,key',
            'status' => 'required|integer|between:0,1',
        ]);

        DB::beginTransaction();
        try {
            if (empty($validated['key'])) {
                $validated['key'] = \Illuminate\Support\Str::slug($validated['name']);
            }

            $category = Category::create($validated);

            DB::commit();

            return redirect()
                ->route('manage.categories.index')
                ->with([
                    'success' => 'Thêm mới thành công danh mục',
                    'new_category_id' => $category->id
                ]);

        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi thêm danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors([
                    'error' => 'Lỗi hệ thống khi thêm mới danh mục. Vui lòng thử lại!'
                ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string|int $id)
    {
        $category = Category::query()->findOrFail($id);
        $platforms = Platform::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();

        return view('admin.management.categories.edit', [
            'dataEdit' => $category,
            'platforms' => $platforms,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $category = Category::query()->findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'platform_id' => 'nullable|exists:platforms,id',
            'description' => 'nullable|string',
            'key' => 'required|string|max:255|unique:categories,key,' . $category->id,
            'status' => 'required|integer|between:0,1',
        ]);

        DB::beginTransaction();
        try {

            $category->update($validated);
            DB::commit();

            return redirect()
                ->route('manage.categories.index')
                ->with('success', 'Cập nhật danh mục thành công.');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi cập nhật danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => 'Lỗi hệ thống khi cập nhật danh mục. Vui lòng thử lại!']);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $category = Category::query()->findOrFail($id);

        DB::beginTransaction();
        try {
            $category->delete();
            DB::commit();

            return redirect()
                ->route('manage.categories.index')
                ->with('success', 'Xóa danh mục thành công.');
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::error('Lỗi khi xóa danh mục: ' . $exception->getMessage());

            return redirect()
                ->back()
                ->withErrors(['error' => 'Lỗi hệ thống khi xóa danh mục. Vui lòng thử lại!']);
        }
    }
}
