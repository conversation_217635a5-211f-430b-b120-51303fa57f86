<?php

namespace App\Http\Controllers\Manage;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Manage\PackageRequest;
use App\Models\Category;
use App\Models\Package;
use App\Models\PackageDetail;
use App\Models\Provider;
use App\Supports\ConnectApiSMM;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;

class PackageDetailController extends Controller
{
    public function index(Request $request)
    {
        \Assets::addScriptsDirectly(['assets/js/manage/package-detail-datatables.min.js']);
        return view('admin.management.package-detail..index');
    }
    public function ajaxData()
    {
        $data = PackageDetail::query()
            ->select('id', 'name', 'category_id', 'member_price','collaborator_price','agency_price', 'distributor_price','status', 'visibility', 'created_at');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'category_id', 'member_price', 'collaborator_price', 'agency_price', 'distributor_price', 'status', 'visibility', 'created_at'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%');
                }
            })
            ->addColumn('category', function ($row) {
                return $row->category->name;
            })
            ->make(true);
    }
    public function create()
    {
        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();

        $packageLists = Package::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();
        return view('admin.management.package-detail.create', [
            'categories' => $categories,
            'providers' => $providers,
            'packageLists' => $packageLists
        ]);
    }

    public function store(PackageRequest $request)
    {
        $package = new Package();

        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('manage.package-details.index'))->withCreatedSuccessMessage();
    }

    public function edit(Package $package)
    {

        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();
        $packageLists = Package::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::ACTIVE->value)
            ->get();
        return view('admin.management.packages.edit', [
            'categories' => $categories,
            'providers' => $providers,
            'dataEdit' =>  $package,
            'packageLists' => $packageLists
        ]);
    }

    public function update(PackageRequest $request, Package $package)
    {
        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('manage.package-details.index'))->withUpdatedSuccessMessage();
    }

    public function destroy(Package $package)
    {
        $package->delete();
        return $this->httpResponse()->setNextUrl(route('manage.package-details.index'))->withDeletedSuccessMessage();
    }

    public function getAllServices(Request $request){
        $payload = $request->validate([
            'provider_id' => 'required|integer',
        ]);
        $app = new ConnectApiSMM();
        $provider = Provider::query()->where('id', $payload['provider_id'])->firstOrFail();
        $service = $app->services($provider->toArray());

        $currentPage = Paginator::resolveCurrentPage();
        $perPage = $request->integer('per_page', 100);
        $collection = collect($service);

        $data = $collection
            ->when($request->query('q'), function (Collection $collection, $search) {
                return $collection->filter(function($item) use ($search) {
                    $searchLower = strtolower($search);
                    return str_contains(strtolower($item['name']), $searchLower) ||
                        str_contains(strtolower($item['service'] ?? ''), $searchLower);
                });
            })
            ->slice(($currentPage - 1) * $perPage, $perPage)
            ->all();

        return $this
            ->httpResponse()
            ->setData((new LengthAwarePaginator($data, count($collection), $perPage))
                ->setPath(route('manage.providers.get-services'))
                ->appends($request->except('page')));
    }

}
