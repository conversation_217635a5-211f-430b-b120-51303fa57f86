<?php

namespace App\Http\Controllers\Manage;

use App\Http\Controllers\Controller;
use App\Models\Package;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/manage/package-datatables.min.js']);
        return view('admin.management.packages.index');
    }
    public function ajaxData()
    {
        $data = Package::query()->select('id', 'name', 'key', 'created_at', 'status');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'key', 'created_at', 'status'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%')
                        ->orWhere('key', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }
    public function create()
    {
        return view('admin.management.packages.create');
    }

    public function store(Request $request)
    {
        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:packages,key',
            'status' => 'required',
        ]);

        Package::query()->create($payload);

        return $this->httpResponse()->setNextUrl(route('manage.packages.index'))->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $packageList = Package::query()->findOrFail($id);
        return view('admin.management.packages.edit', [
            'dataEdit' => $packageList,
        ]);
    }

    public function update(string|int $id, Request $request)
    {
        $packageList = Package::query()->findOrFail($id);

        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:packages,key,' . $id,
            'status' => 'required',
        ]);

        $packageList->update($payload);

        return $this->httpResponse()->setNextUrl(route('manage.packages.index'))->withUpdatedSuccessMessage();
    }
    public function destroy(string|int $id)
    {
        $packageList = Package::query()->findOrFail($id);
        $packageList->delete();

        return $this->httpResponse()->setNextUrl(route('manage.packages.index'))->withDeletedSuccessMessage();
    }
}
