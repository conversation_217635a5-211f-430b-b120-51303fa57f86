<?php

namespace App\Http\Controllers\Manage;

use Illuminate\Http\Request;
use App\Models\LatestFeature;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class LatestFeaturesController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/manage/latest-feature-datatables.min.js']);
        return view('admin.management.latest-features.index');
    }

    public function ajaxData()
    {
        $data = LatestFeature::query()
            ->select('id', 'name','content','image', 'created_at');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'content','created_at'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.management.latest-features.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
        ]);

        LatestFeature::query()->create([
            'name' => $validated['name'],
            'content' => $validated['content'],
            'image' => $validated['image'],
        ]);

        return $this->httpResponse()->setNextRoute('manage.latest-features.index')->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $latestFeatures = LatestFeature::query()->findOrFail($id);
        return view('admin.management.latest-features.edit', [
            'dataEdit' => $latestFeatures,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string|int $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
        ]);
        $latestFeatures = LatestFeature::query()->findOrFail($id);
        $latestFeatures->update([
            'name' => $validated['name'],
            'content' => $validated['content'],
            'image' => $validated['image'],
        ]);

        return $this->httpResponse()->setNextRoute('manage.latest-features.index')->withUpdatedSuccessMessage();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string|int $id)
    {
        $query = LatestFeature::query()->findOrFail($id);
        $query->delete();

        return $this->httpResponse()->setNextRoute('manage.platform.index')->withDeletedSuccessMessage();
    }
}
