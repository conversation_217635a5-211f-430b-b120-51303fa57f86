<?php

namespace App\Http\Controllers\Manage;

use App\Enums\PinHomeNotificationEnum;
use App\Models\Tenant;
use Illuminate\Http\Request;
use App\Enums\BaseStatusEnum;
use App\Models\HomeNotification;
use App\Http\Controllers\Controller;
use Illuminate\Validation\Rules\Enum;
use Yajra\DataTables\Facades\DataTables;

class HomeNotificationController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/manage/home-notification-datatables.min.js']);
        return view('admin.management.home-notifications.index');
    }

    public function ajaxData()
    {
        $data = HomeNotification::query()
            ->select('id', 'tenant_id','name','image', 'created_at','pin', 'status');

        if (request()->has('order_by') && request()->has('order_dir')) {
            $allowedColumns = ['id', 'name', 'content','created_at'];
            $orderBy = request('order_by');
            $orderDir = request('order_dir');
            if (in_array($orderBy, $allowedColumns)) {
                $data->orderBy($orderBy, $orderDir);
            }
        }

        return DataTables::of($data)
            ->addColumn('tenant',function($row){
                return $row->tenant->domain ?? 'N/A';
            })
            ->filter(function ($query) {
                if (request('keyword')) {
                    $query->where('name', 'like', '%' . request('keyword') . '%');
                }
            })
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tenants = Tenant::query()->select('id', 'domain','status')->where('status', BaseStatusEnum::ACTIVE->value)->get();
        return view('admin.management.home-notifications.create',[
            'tenants' => $tenants
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
            'pin' => ['required', new Enum(PinHomeNotificationEnum::class)],
            'status' => 'required|in:0,1',
        ]);

        HomeNotification::query()->create([
            'tenant_id' => $validated['tenant_id'],
            'name' => $validated['name'],
            'content' => $validated['content'],
            'image' => $validated['image'] ?? null,
            'pin' => $validated['pin'],
            'status' => $validated['status'],
        ]);

        return $this->httpResponse()
            ->setNextRoute('manage.home-notifications.index')
            ->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $homeNotification = HomeNotification::query()->findOrFail($id);
        $tenants = Tenant::query()->select('id', 'domain','status')->where('status', BaseStatusEnum::ACTIVE->value)->get();

        return view('admin.management.home-notifications.edit', [
            'dataEdit' => $homeNotification,
            'tenants' => $tenants
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string|int $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|string',
            'tenant_id' => 'required|exists:tenants,id',
        ]);
        $HomeNotifications = HomeNotification::query()->findOrFail($id);
        $HomeNotifications->update([
            'tenant_id' => $validated['tenant_id'],
            'name' => $validated['name'],
            'content' => $validated['content'],
            'image' => $validated['image'],
        ]);

        return $this->httpResponse()->setNextRoute('manage.home-notifications.index')->withUpdatedSuccessMessage();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string|int $id)
    {
        $query = HomeNotification::query()->findOrFail($id);
        $query->delete();

        return $this->httpResponse()->setNextRoute('manage.home-notifications.index')->withDeletedSuccessMessage();
    }
}
