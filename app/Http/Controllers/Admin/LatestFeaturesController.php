<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LatestFeature;
use Illuminate\Http\Request;

class LatestFeaturesController extends Controller
{
    public function index()
    {
        $query = LatestFeature::query()
            ->select('id', 'name', 'content', 'created_at')
            ->orderBy('id', 'desc')
            ->get();
        return view('admin.latest-feature.index', [
            'data' => $query
        ]);
    }

    public function detail(string|int $id)
    {
        $data = LatestFeature::query()->select('id', 'content', 'image')->findOrFail($id);

        return $this
            ->httpResponse()
            ->setMessage('Thao tác thành công')
            ->setData($data)
            ->toApiResponse();

    }
}
