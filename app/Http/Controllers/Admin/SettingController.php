<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TenantSettingRequest;
use Illuminate\Http\Request;

class SettingController extends Controller
{

    protected $tenant;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $this->tenant = app('tenant');
            
            if (!$this->tenant) {
                abort(500, 'Tenant context not found');
            }
            
            return $next($request);
        });
    }
    public function index()
    {
        $settings = tenant()->settings()
                    ->pluck('value', 'key')
                    ->toArray();
        return view('admin.setting.index', [
            'settings' => $settings
        ]);
    }

    public function update(TenantSettingRequest $request)
    {
        $settings = $request->validated()['settings'] ?? [];

        foreach ($settings as $key => $value) {
            $this->tenant->settings()->updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return $this->httpResponse()->setNextRoute('admin.settings.index')->withUpdatedSuccessMessage();
    }
}
