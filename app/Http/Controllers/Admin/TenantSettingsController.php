<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Concerns\HasHttpResponse;
use App\Http\Requests\Admin\TenantSettingsRequest;
use App\Services\TenantSettingsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class TenantSettingsController extends Controller
{
    use HasHttpResponse;

    protected TenantSettingsService $tenantSettingsService;

    public function __construct(TenantSettingsService $tenantSettingsService)
    {
        $this->tenantSettingsService = $tenantSettingsService;
    }

    /**
     * Display the tenant settings management interface.
     */
    public function index(): View
    {
        // Ensure user has proper permissions
        if (!user_can_access_admin()) {
            abort(403, 'Access denied: Admin privileges required');
        }

        $tenantId = tenant_id();
        if (!$tenantId) {
            abort(404, 'Tenant context not found');
        }

        // Get all settings grouped by category
        $settingsByCategory = $this->tenantSettingsService->getSettingsByCategory($tenantId);
        
        // Get configuration data for form rendering
        $categories = config('tenant-settings.categories', []);
        $categoryLabels = config('tenant-settings.category_labels', []);
        $metadata = config('tenant-settings.metadata', []);
        $types = config('tenant-settings.types', []);
        $selectOptions = config('tenant-settings.select_options', []);

        return view('admin.tenant-settings.index', compact(
            'settingsByCategory',
            'categories',
            'categoryLabels',
            'metadata',
            'types',
            'selectOptions'
        ));
    }

    /**
     * Update tenant settings.
     */
    public function update(TenantSettingsRequest $request): JsonResponse|RedirectResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $settings = $request->validated()['settings'] ?? [];
            
            // Process each setting
            $results = [];
            foreach ($settings as $key => $value) {
                // Handle boolean values from checkboxes
                if (config("tenant-settings.types.{$key}") === 'boolean') {
                    $value = $value === '1' || $value === 'on' || $value === true;
                }
                
                $results[$key] = $this->tenantSettingsService->setSetting($tenantId, $key, $value);
            }

            if ($request->expectsJson()) {
                return $this->httpResponseSuccess('Settings updated successfully', $results);
            }

            return redirect()
                ->route('admin.tenant-settings.index')
                ->with('success', 'Cài đặt đã được cập nhật thành công!');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return $this->httpResponseError('Failed to update settings: ' . $e->getMessage());
            }

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Có lỗi xảy ra khi cập nhật cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Get a specific setting value.
     */
    public function show(Request $request, string $key): JsonResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $value = $this->tenantSettingsService->getSetting($tenantId, $key);
            $metadata = $this->tenantSettingsService->getSettingMetadata($key);

            return $this->httpResponseSuccess('Setting retrieved successfully', [
                'key' => $key,
                'value' => $value,
                'metadata' => $metadata
            ]);

        } catch (\Exception $e) {
            return $this->httpResponseError('Failed to retrieve setting: ' . $e->getMessage());
        }
    }

    /**
     * Reset a specific setting to its default value.
     */
    public function reset(Request $request, string $key): JsonResponse|RedirectResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $this->tenantSettingsService->resetSetting($tenantId, $key);

            if ($request->expectsJson()) {
                return $this->httpResponseSuccess('Setting reset to default successfully');
            }

            return redirect()
                ->route('admin.tenant-settings.index')
                ->with('success', 'Cài đặt đã được khôi phục về mặc định!');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return $this->httpResponseError('Failed to reset setting: ' . $e->getMessage());
            }

            return redirect()
                ->back()
                ->with('error', 'Có lỗi xảy ra khi khôi phục cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Reset all settings to their default values.
     */
    public function resetAll(Request $request): JsonResponse|RedirectResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $this->tenantSettingsService->resetAllSettings($tenantId);

            if ($request->expectsJson()) {
                return $this->httpResponseSuccess('All settings reset to defaults successfully');
            }

            return redirect()
                ->route('admin.tenant-settings.index')
                ->with('success', 'Tất cả cài đặt đã được khôi phục về mặc định!');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return $this->httpResponseError('Failed to reset all settings: ' . $e->getMessage());
            }

            return redirect()
                ->back()
                ->with('error', 'Có lỗi xảy ra khi khôi phục tất cả cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Export tenant settings as JSON.
     */
    public function export(): JsonResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $settings = $this->tenantSettingsService->getAllSettings($tenantId);
            
            return response()->json($settings)
                ->header('Content-Disposition', 'attachment; filename="tenant-settings-' . $tenantId . '.json"');

        } catch (\Exception $e) {
            return $this->httpResponseError('Failed to export settings: ' . $e->getMessage());
        }
    }

    /**
     * Import tenant settings from JSON.
     */
    public function import(Request $request): JsonResponse|RedirectResponse
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:2048',
        ]);

        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $file = $request->file('settings_file');
            $content = file_get_contents($file->getPathname());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \InvalidArgumentException('Invalid JSON file');
            }

            $results = $this->tenantSettingsService->importSettings($tenantId, $settings);

            if ($request->expectsJson()) {
                return $this->httpResponseSuccess('Settings imported successfully', $results);
            }

            return redirect()
                ->route('admin.tenant-settings.index')
                ->with('success', 'Cài đặt đã được nhập thành công!');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return $this->httpResponseError('Failed to import settings: ' . $e->getMessage());
            }

            return redirect()
                ->back()
                ->with('error', 'Có lỗi xảy ra khi nhập cài đặt: ' . $e->getMessage());
        }
    }

    /**
     * Clear settings cache for the current tenant.
     */
    public function clearCache(): JsonResponse
    {
        $tenantId = tenant_id();
        if (!$tenantId) {
            return $this->httpResponseError('Tenant context not found', 404);
        }

        try {
            $this->tenantSettingsService->clearCache($tenantId);
            
            return $this->httpResponseSuccess('Settings cache cleared successfully');

        } catch (\Exception $e) {
            return $this->httpResponseError('Failed to clear cache: ' . $e->getMessage());
        }
    }
}
