<?php

namespace App\Supports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
class ConnectApiSMM
{
    private const CACHE_DURATION = 15; // minutes

    /**
     * Get services with caching
     *
     * @param array $apiParams
     * @return array|null
     */
    public function services(array $apiParams): ?array
    {
        $cacheKey = "smm_services_{$apiParams['id']}";

        return Cache::remember($cacheKey, Carbon::now()->addMinutes(self::CACHE_DURATION), function () use ($apiParams) {
            return (new Standard($apiParams))->services();
        });
    }

    /**
     * Create new order
     *
     * @param array $apiParams
     * @param array $dataPost
     * @return array|null
     */
    public function order(array $apiParams, array $dataPost): ?array
    {
        return (new Standard($apiParams))->order($dataPost);
    }

    /**
     * Get status of single order
     *
     * @param array $apiParams
     * @param string|int $orderID
     * @return array|null
     */
    public function status(array $apiParams, $orderID): ?array
    {
        return (new Standard($apiParams))->status($orderID);
    }

    /**
     * Get status of multiple orders
     *
     * @param array $apiParams
     * @param array $orderIDs
     * @return array|null
     */
    public function multiStatus(array $apiParams, array $orderIDs): ?array
    {
        return (new Standard($apiParams))->multiStatus($orderIDs);
    }

    /**
     * Get account balance
     *
     * @param array $apiParams
     * @return array|null
     */
    public function balance(array $apiParams): ?array
    {
        return (new Standard($apiParams))->balance();
    }

}
