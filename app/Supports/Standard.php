<?php

namespace App\Supports;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Log;

class Standard
{
    private string $apiUrl;
    private string $apiKey;
    private int $timeout;

    public function __construct(array $apiParams)
    {
        $this->apiUrl = $apiParams['url'];
        $this->apiKey = $apiParams['key'];
        $this->timeout = $apiParams['timeout'] ?? 60;
    }

    /**
     * Create new order
     *
     * @param array $data
     * @return array|null
     */
    public function order(array $data): ?array
    {
        return $this->sendRequest('add', $data);
    }

    /**
     * Get status of single order
     *
     * @param string|int $orderID
     * @return array|null
     */
    public function status($orderID): ?array
    {
        return $this->sendRequest('status', ['order' => $orderID]);
    }

    /**
     * Get status of multiple orders
     *
     * @param array $orderIDs
     * @return array|null
     */
    public function multiStatus(array $orderIDs): ?array
    {
        return $this->sendRequest('status', ['orders' => implode(',', $orderIDs)]);
    }

    /**
     * Get available services
     *
     * @return array|null
     */
    public function services(): ?array
    {
        return $this->sendRequest('services');
    }

    /**
     * Get account balance
     *
     * @return array|null
     */
    public function balance(): ?array
    {
        return $this->sendRequest('balance');
    }

    /**
     * Send request to API
     *
     * @param string $action
     * @param array $additionalData
     * @return array|null
     */
    private function sendRequest(string $action, array $additionalData = []): ?array
    {
        try {
            $data = array_merge(
                [
                    'key' => $this->apiKey,
                    'action' => $action
                ],
                $additionalData
            );

            $response = Http::withOptions([
                'verify' => false,
                'timeout' => $this->timeout,
            ])
                ->withHeaders([
                    'User-Agent' => 'API Client/1.0',
                    'Accept' => 'application/json',
                ])
                ->asForm()
                ->post($this->apiUrl, $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('SMM API Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'request' => $data
            ]);

            return null;

        } catch (RequestException $e) {
            Log::error('Ngoại lệ yêu cầu API MMS', [
                'message' => $e->getMessage(),
                'request' => $data ?? []
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Ngoại lệ chung của API SMMS', [
                'message' => $e->getMessage(),
                'request' => $data ?? []
            ]);
            return null;
        }
    }
}
